import { EnvVarWarning } from "@/components/env-var-warning";
import { hasEnvVars } from "@/supabase/client/check-env-vars";
import { Plus_Jakarta_Sans, Lora, Roboto_Mono } from 'next/font/google';
import { ThemeProvider } from "next-themes";
import { Toaster as ShadcnToaster } from "@/components/ui/toaster";
import "./globals.css";
import { Analytics } from "@vercel/analytics/react"
import type { Metadata } from 'next';
import { cn } from "@/lib/utils";

// Determine the base URL - prioritize production URL
const prodUrl = process.env.VERCEL_PROJECT_PRODUCTION_URL
  ? `https://${process.env.VERCEL_PROJECT_PRODUCTION_URL}`
  : null;
const vercelUrl = process.env.VERCEL_URL
  ? `https://${process.env.VERCEL_URL}`
  : null;
const baseUrl = prodUrl || vercelUrl || 'http://localhost:3000';

export const metadata: Metadata = {
  metadataBase: new URL(baseUrl),
  title: "Dashboard",
  description: "Interior dashboard application for app developers and founders.",
  icons: {
    icon: "/logo.png",
  },
  openGraph: {
    title: "Dashboard",
    description: "Interior dashboard application for app developers and founders.",
    images: ['/preview.png'],
    url: baseUrl,
    siteName: 'Dashboard',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "Dashboard",
    description: "Interior dashboard application for app developers and founders.",
    images: ['/preview.png'],
  }
};

const fontSans = Plus_Jakarta_Sans({
  subsets: ['latin'],
  variable: '--font-sans',
  display: 'swap',
});

const fontSerif = Lora({
  subsets: ['latin'],
  variable: '--font-serif',
  display: 'swap',
});

const fontMono = Roboto_Mono({
  subsets: ['latin'],
  variable: '--font-mono',
  display: 'swap',
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en"
          className={cn(
            fontSans.variable,
            fontSerif.variable,
            fontMono.variable,
          )}
          suppressHydrationWarning>
      <body className="bg-background text-foreground">
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          {children}
          <ShadcnToaster />
        </ThemeProvider>
        <Analytics />
      </body>
    </html>
  );
}
