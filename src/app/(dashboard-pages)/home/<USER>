"use client";

import React from "react";
import {
  B<PERSON><PERSON><PERSON>b,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Separator } from "@/components/ui/separator"

// Updated imports to point to src/components/dashboard
import { StrackrOverview } from "@/components/dashboard/strackr-overview"
import { ShopMyOverview } from "@/components/dashboard/shopmy-overview"

// Import the hook for analytics data
import { usePlatformAnalytics } from "@/hooks/usePlatformAnalytics";

// No date adjustments needed - use dates as-is

export default function HomePage() {
  // State for time ranges - each platform can have its own time range
  const [strackrTimeRange, setStrackrTimeRange] = React.useState("all");
  const [shopMyTimeRange, setShopMyTimeRange] = React.useState("all");

  // Fetch analytics data for both platforms with time ranges only
  const {
    data: strackrData,
    loading: strackrLoading,
    error: strackrError,
    refetch: refetchStrackr
  } = usePlatformAnalytics('strackr', strackrTimeRange);

  const {
    data: shopMyData,
    loading: shopMyLoading,
    error: shopMyError,
    refetch: refetchShopMy
  } = usePlatformAnalytics('shopmy', shopMyTimeRange);

  // Handle time range changes
  const handleStrackrTimeRangeChange = React.useCallback((timeRange: string) => {
    setStrackrTimeRange(timeRange);
  }, []);

  const handleShopMyTimeRangeChange = React.useCallback((timeRange: string) => {
    setShopMyTimeRange(timeRange);
  }, []);

  return (
    <div className="@container/main flex flex-1 flex-col gap-4 p-4 md:gap-6 md:p-6">
      {/* Breadcrumbs and Separator first (or wherever they were originally) */}
      <Breadcrumb className="hidden font-medium md:flex">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="#">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Overview</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      <Separator className="my-2 hidden md:block" />

      {/* Two-column grid for Strackr and ShopMy analytics */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-6">
        {/* Column 1: Strackr Overview */}
        <StrackrOverview
          analyticsData={strackrData}
          loading={strackrLoading}
          error={strackrError}
          timeRange={strackrTimeRange}
          onTimeRangeChange={handleStrackrTimeRangeChange}
        />

        {/* Column 2: ShopMy Overview */}
        <ShopMyOverview
          analyticsData={shopMyData}
          loading={shopMyLoading}
          error={shopMyError}
          timeRange={shopMyTimeRange}
          onTimeRangeChange={handleShopMyTimeRangeChange}
        />
      </div>
    </div>
  );
}
