"use client";

import React from "react";
import { SpreadsheetDataTable } from "@/components/dashboard/spreadsheet-data";
import { TransactionFilters } from "@/components/dashboard/transaction-filters";

export default function SpreadsheetPage() {
  const [filters, setFilters] = React.useState<{
    platform?: string;
    merchant?: string;
    status?: string;
    network?: string;
    startDate?: string;
    endDate?: string;
  }>({});

  const handleFiltersChange = React.useCallback((newFilters: typeof filters) => {
    setFilters(newFilters);
  }, []);

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold mb-2">Transaction Spreadsheet</h1>
        <p className="text-gray-600">
          View and analyze your affiliate marketing transaction data in real-time.
          Use filters to narrow down results and scroll to load more data automatically.
        </p>
      </div>

      <TransactionFilters
        onFiltersChange={handleFiltersChange}
        initialFilters={filters}
      />

      <SpreadsheetDataTable initialFilters={filters} />
    </div>
  );
}
