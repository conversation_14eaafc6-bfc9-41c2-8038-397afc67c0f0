import { signInWithGoogleAction } from "@/app/actions";
import { FormMessage, Message } from "@/components/form-message";
import { SubmitButton } from "@/components/submit-button";
import { Icons } from "@/components/ui/icons";
import Link from "next/link";
import Image from "next/image";

export default async function SignInPage(props: { searchParams: Promise<Message> }) {
  const searchParams = await props.searchParams;

  return (
    <>
      <div className="absolute top-0 left-0 p-4 md:p-6 lg:p-8">
        <Link href={"/"} className="flex items-center">
          <Image
            src="/logo.png"
            alt="Logo"
            width={60}
            height={60}
            className="filter invert brightness-0 dark:filter-none dark:invert-0"
          />
        </Link>
      </div>

      <div className="flex-1 flex flex-col w-full justify-center items-center gap-6">
        <h1 className="text-4xl font-semibold text-white">Login</h1>
        <form action={signInWithGoogleAction} className="w-full max-w-xs">
          <SubmitButton
            pendingText="Redirecting..."
            className="w-full bg-zinc-800 hover:bg-zinc-700 text-white border border-zinc-700 flex items-center justify-center gap-2"
          >
            <Icons.google className="h-5 w-5" />
            Continue with Google
          </SubmitButton>
          <FormMessage message={searchParams} />
        </form>
      </div>
    </>
  );
}
