import { NextRequest, NextResponse } from 'next/server';
import { getPlatformAnalyticsData } from '@/app/(dashboard-pages)/home/<USER>';

export async function GET(request: NextRequest) {
  try {
    // Add cache control headers to ensure fresh data
    const headers = {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    };

    const { searchParams } = new URL(request.url);
    const platform = searchParams.get('platform');
    const timeRange = searchParams.get('timeRange') || 'all';
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    if (!platform) {
      return NextResponse.json(
        { error: 'Platform parameter is required' },
        { status: 400, headers }
      );
    }

    // Validate platform
    if (!['strackr', 'shopmy'].includes(platform.toLowerCase())) {
      return NextResponse.json(
        { error: 'Invalid platform. Must be "strackr" or "shopmy"' },
        { status: 400, headers }
      );
    }

    // Validate timeRange
    if (!['all', '7d', '30d', '90d'].includes(timeRange)) {
      return NextResponse.json(
        { error: 'Invalid timeRange. Must be "all", "7d", "30d", or "90d"' },
        { status: 400, headers }
      );
    }

    // Validate date format if provided
    if (startDate && !/^\d{4}-\d{2}-\d{2}$/.test(startDate)) {
      return NextResponse.json(
        { error: 'Invalid startDate format. Must be YYYY-MM-DD' },
        { status: 400, headers }
      );
    }

    if (endDate && !/^\d{4}-\d{2}-\d{2}$/.test(endDate)) {
      return NextResponse.json(
        { error: 'Invalid endDate format. Must be YYYY-MM-DD' },
        { status: 400, headers }
      );
    }

    // Validate date range logic
    if (startDate && endDate && new Date(startDate) > new Date(endDate)) {
      return NextResponse.json(
        { error: 'startDate cannot be after endDate' },
        { status: 400, headers }
      );
    }

    // Fetch analytics data using the existing server action
    const { data, error } = await getPlatformAnalyticsData(
      platform.toLowerCase(),
      timeRange,
      startDate || undefined,
      endDate || undefined
    );

    if (error) {
      return NextResponse.json(
        { error },
        { status: 500, headers }
      );
    }

    return NextResponse.json({ data }, { headers });

  } catch (error) {
    console.error('Platform analytics API route error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
