import { useState, useEffect, useCallback } from 'react';
import { NormalizedTransaction, TransactionApiResponse, TransactionDisplayData } from '@/types/database';

interface UseTransactionsOptions {
  page?: number;
  limit?: number;
  platform?: string;
  merchant?: string;
  status?: string;
  network?: string;
  startDate?: string;
  endDate?: string;
  autoFetch?: boolean;
}

interface UseTransactionsReturn {
  data: TransactionDisplayData[];
  loading: boolean;
  error: string | null;
  totalCount: number;
  currentPage: number;
  totalPages: number;
  refetch: () => Promise<void>;
  setFilters: (filters: Partial<UseTransactionsOptions>) => void;
  setPage: (page: number) => void;
  nextPage: () => void;
  previousPage: () => void;
  canNextPage: boolean;
  canPreviousPage: boolean;
}

// Transform raw transaction data to display format
function transformTransactionData(transaction: NormalizedTransaction): TransactionDisplayData {
  return {
    id: transaction.id,
    date: transaction.transaction_date,
    platform: transaction.platform,
    merchant: transaction.merchant_name,
    network: transaction.network_name,
    status: transaction.status,
    order_amount: transaction.order_amount,
    commission_amount: transaction.commission_amount,
    currency: transaction.currency,
    transaction_type: transaction.transaction_type,
    order_id: transaction.order_id,
    customer_id: transaction.customer_id,
  };
}

export function useTransactions(options: UseTransactionsOptions = {}): UseTransactionsReturn {
  const {
    page = 1,
    limit = 50,
    platform,
    merchant,
    status,
    network,
    startDate,
    endDate,
    autoFetch = true
  } = options;

  const [data, setData] = useState<TransactionDisplayData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(page);
  const [filters, setFiltersState] = useState({
    platform,
    merchant,
    status,
    network,
    startDate,
    endDate
  });

  const fetchTransactions = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      // Build query parameters
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: limit.toString(),
      });

      // Add filters if they exist
      if (filters.platform) params.append('platform', filters.platform);
      if (filters.merchant) params.append('merchant', filters.merchant);
      if (filters.status) params.append('status', filters.status);
      if (filters.network) params.append('network', filters.network);
      if (filters.startDate) params.append('startDate', filters.startDate);
      if (filters.endDate) params.append('endDate', filters.endDate);

      const response = await fetch(`/api/transactions?${params.toString()}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: TransactionApiResponse = await response.json();

      if (result.error) {
        throw new Error(result.error);
      }

      // Transform the data for display
      const transformedData = result.data.map(transformTransactionData);

      setData(transformedData);
      setTotalCount(result.count || 0);

    } catch (err) {
      let errorMessage = 'Failed to fetch transactions';

      if (err instanceof Error) {
        errorMessage = err.message;
      } else if (typeof err === 'string') {
        errorMessage = err;
      }

      // Add more specific error messages based on common issues
      if (errorMessage.includes('fetch')) {
        errorMessage = 'Network error: Unable to connect to the server. Please check your internet connection.';
      } else if (errorMessage.includes('500')) {
        errorMessage = 'Server error: There was a problem processing your request. Please try again later.';
      } else if (errorMessage.includes('401') || errorMessage.includes('403')) {
        errorMessage = 'Authentication error: Please refresh the page and try again.';
      }

      setError(errorMessage);
      console.error('Error fetching transactions:', err);
    } finally {
      setLoading(false);
    }
  }, [currentPage, limit, filters]);

  // Update filters and reset to page 1
  const setFilters = useCallback((newFilters: Partial<UseTransactionsOptions>) => {
    setFiltersState(prev => {
      // Only update if filters actually changed
      const hasChanged = Object.keys(newFilters).some(key =>
        prev[key as keyof typeof prev] !== newFilters[key as keyof typeof newFilters]
      );
      if (hasChanged) {
        setCurrentPage(1);
        return { ...prev, ...newFilters };
      }
      return prev;
    });
  }, []);

  // Calculate total pages and navigation states first
  const totalPages = Math.ceil(totalCount / limit);
  const canNextPage = currentPage < totalPages;
  const canPreviousPage = currentPage > 1;

  // Page navigation functions
  const setPage = useCallback((page: number) => {
    const maxPages = Math.ceil(totalCount / limit);
    if (page >= 1 && page <= maxPages) {
      setCurrentPage(page);
    }
  }, [totalCount, limit]);

  const nextPage = useCallback(() => {
    const maxPages = Math.ceil(totalCount / limit);
    if (currentPage < maxPages) {
      setCurrentPage(prev => prev + 1);
    }
  }, [currentPage, totalCount, limit]);

  const previousPage = useCallback(() => {
    if (currentPage > 1) {
      setCurrentPage(prev => prev - 1);
    }
  }, [currentPage]);

  // Auto-fetch on mount and when dependencies change
  useEffect(() => {
    if (autoFetch) {
      fetchTransactions();
    }
  }, [fetchTransactions, autoFetch]);

  // Update current page when page option changes
  useEffect(() => {
    setCurrentPage(page);
  }, [page]);

  return {
    data,
    loading,
    error,
    totalCount,
    currentPage,
    totalPages,
    refetch: fetchTransactions,
    setFilters,
    setPage,
    nextPage,
    previousPage,
    canNextPage,
    canPreviousPage,
  };
}

// Hook for summary data
interface UseSummaryOptions {
  type?: 'daily' | 'merchant' | 'transaction';
  platform?: string;
  startDate?: string;
  endDate?: string;
  limit?: number;
}

export function useSummaryData(options: UseSummaryOptions = {}) {
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchSummary = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();

      if (options.type) params.append('type', options.type);
      if (options.platform) params.append('platform', options.platform);
      if (options.startDate) params.append('startDate', options.startDate);
      if (options.endDate) params.append('endDate', options.endDate);
      if (options.limit) params.append('limit', options.limit.toString());

      const response = await fetch(`/api/transactions/summary?${params.toString()}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.error) {
        throw new Error(result.error);
      }

      setData(result.data || []);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch summary data';
      setError(errorMessage);
      console.error('Error fetching summary:', err);
    } finally {
      setLoading(false);
    }
  }, [options.type, options.platform, options.startDate, options.endDate, options.limit]);

  useEffect(() => {
    fetchSummary();
  }, [fetchSummary]);

  return {
    data,
    loading,
    error,
    refetch: fetchSummary,
  };
}
