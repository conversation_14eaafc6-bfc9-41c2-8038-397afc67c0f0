"use client";

import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { processUpload } from '@/lib/client-csv-processor';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  UploadIcon,
  FileIcon,
  CheckCircleIcon,
  XCircleIcon,
  AlertCircleIcon,
  LoaderIcon
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface UploadStatus {
  status: 'idle' | 'uploading' | 'processing' | 'success' | 'error';
  progress: number;
  message: string;
  uploadId?: string;
  details?: {
    totalRows?: number;
    processedRows?: number;
    successfulRows?: number;
    errorRows?: number;
    filteredRows?: number;
    duplicateRows?: number;
  };
}

export function CsvUploadDropzone() {
  const [uploadStatus, setUploadStatus] = useState<UploadStatus>({
    status: 'idle',
    progress: 0,
    message: ''
  });

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (!file) return;

    // Validate file
    if (!file.name.toLowerCase().endsWith('.csv')) {
      setUploadStatus({
        status: 'error',
        progress: 0,
        message: 'Please select a CSV file'
      });
      return;
    }

    if (file.size > 10 * 1024 * 1024) { // 10MB
      setUploadStatus({
        status: 'error',
        progress: 0,
        message: 'File size must be less than 10MB'
      });
      return;
    }

    try {
      // Start upload
      setUploadStatus({
        status: 'uploading',
        progress: 0,
        message: 'Uploading file...'
      });

      const formData = new FormData();
      formData.append('file', file);
      formData.append('platform', 'shopmy');

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Upload failed');
      }

      const result = await response.json();

      setUploadStatus({
        status: 'processing',
        progress: 0,
        message: 'Starting CSV processing...',
        uploadId: result.uploadId,
        details: {
          totalRows: result.totalRows
        }
      });

      // Start chunked processing
      startChunkedProcessing(result.uploadId, result.totalRows);

    } catch (error) {
      setUploadStatus({
        status: 'error',
        progress: 0,
        message: error instanceof Error ? error.message : 'Upload failed'
      });
    }
  }, []);

  const startChunkedProcessing = async (uploadId: string, _totalRows: number) => {
    try {
      await processUpload(uploadId, (progress) => {
        const filteredCount = progress.filteredRows || 0;

        let successMessage = `Successfully processed ${progress.successfulRows} transactions`;
        if (filteredCount > 0) {
          successMessage += ` (${filteredCount} filtered)`;
        }

        setUploadStatus({
          status: progress.isComplete ? 'success' : 'processing',
          progress: progress.progress,
          message: progress.isComplete
            ? successMessage
            : `Processing... ${progress.processedRows} of ${progress.totalRows} rows`,
          uploadId,
          details: {
            totalRows: progress.totalRows,
            processedRows: progress.processedRows,
            successfulRows: progress.successfulRows,
            errorRows: progress.errorRows,
            filteredRows: progress.filteredRows,
            duplicateRows: progress.duplicateRows
          }
        });
      });
    } catch (error) {
      setUploadStatus({
        status: 'error',
        progress: 0,
        message: error instanceof Error ? error.message : 'Processing failed',
        uploadId
      });
    }
  };



  const resetUpload = () => {
    setUploadStatus({
      status: 'idle',
      progress: 0,
      message: ''
    });
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'application/vnd.ms-excel': ['.csv']
    },
    maxFiles: 1,
    disabled: uploadStatus.status === 'uploading' || uploadStatus.status === 'processing'
  });

  const getStatusIcon = () => {
    switch (uploadStatus.status) {
      case 'uploading':
      case 'processing':
        return <LoaderIcon className="h-8 w-8 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircleIcon className="h-8 w-8 text-green-500" />;
      case 'error':
        return <XCircleIcon className="h-8 w-8 text-red-500" />;
      default:
        return <UploadIcon className="h-8 w-8 text-muted-foreground" />;
    }
  };

  const getStatusColor = () => {
    switch (uploadStatus.status) {
      case 'success':
        return 'border-green-200 bg-green-50';
      case 'error':
        return 'border-red-200 bg-red-50';
      case 'uploading':
      case 'processing':
        return 'border-blue-200 bg-blue-50';
      default:
        return isDragActive ? 'border-blue-300 bg-blue-50' : 'border-dashed border-gray-300';
    }
  };

  return (
    <div className="space-y-4">
      {/* Drop Zone */}
      <div
        {...getRootProps()}
        className={cn(
          "relative cursor-pointer rounded-lg border-2 p-8 text-center transition-colors",
          getStatusColor(),
          uploadStatus.status === 'idle' && "hover:border-gray-400"
        )}
      >
        <input {...getInputProps()} />

        <div className="flex flex-col items-center space-y-4">
          {getStatusIcon()}

          <div className="space-y-2">
            {uploadStatus.status === 'idle' ? (
              <>
                <p className="text-lg font-medium">
                  {isDragActive ? 'Drop your CSV file here' : 'Upload CSV File'}
                </p>
                <p className="text-sm text-muted-foreground">
                  Drag and drop your Shopmy CSV file here, or click to browse
                </p>
              </>
            ) : (
              <>
                <p className="text-lg font-medium">{uploadStatus.message}</p>
                {uploadStatus.details && (
                  <div className="flex flex-wrap gap-2 justify-center">
                    {uploadStatus.details.totalRows && (
                      <Badge variant="outline">
                        Total: {uploadStatus.details.totalRows}
                      </Badge>
                    )}
                    {uploadStatus.details.successfulRows !== undefined && (
                      <Badge variant="outline" className="text-green-600">
                        Success: {uploadStatus.details.successfulRows}
                      </Badge>
                    )}
                    {uploadStatus.details.errorRows !== undefined && uploadStatus.details.errorRows > 0 && (
                      <Badge variant="outline" className="text-red-600">
                        Errors: {uploadStatus.details.errorRows}
                      </Badge>
                    )}
                    {uploadStatus.details.filteredRows !== undefined && uploadStatus.details.filteredRows > 0 && (
                      <Badge variant="outline" className="text-yellow-600">
                        Filtered: {uploadStatus.details.filteredRows}
                      </Badge>
                    )}
                  </div>
                )}
              </>
            )}
          </div>

          {uploadStatus.status === 'idle' && (
            <Button variant="outline" size="sm">
              <FileIcon className="mr-2 h-4 w-4" />
              Choose File
            </Button>
          )}
        </div>
      </div>

      {/* Progress Bar */}
      {(uploadStatus.status === 'uploading' || uploadStatus.status === 'processing') && (
        <div className="space-y-2">
          <Progress value={uploadStatus.progress} className="w-full" />
          <p className="text-sm text-center text-muted-foreground">
            {uploadStatus.progress}% complete
          </p>
        </div>
      )}

      {/* Status Messages */}
      {uploadStatus.status === 'success' && (
        <Alert>
          <CheckCircleIcon className="h-4 w-4" />
          <AlertDescription>
            File uploaded successfully! Your data has been imported and is now available in your dashboard.
          </AlertDescription>
        </Alert>
      )}

      {uploadStatus.status === 'error' && (
        <Alert variant="destructive">
          <AlertCircleIcon className="h-4 w-4" />
          <AlertDescription>
            {uploadStatus.message}
          </AlertDescription>
        </Alert>
      )}

      {/* Action Buttons */}
      {(uploadStatus.status === 'success' || uploadStatus.status === 'error') && (
        <div className="flex justify-center space-x-2">
          <Button onClick={resetUpload} variant="outline">
            Upload Another File
          </Button>
          {uploadStatus.uploadId && uploadStatus.details?.errorRows && uploadStatus.details.errorRows > 0 && (
            <Button
              variant="outline"
              onClick={() => {
                // TODO: Open error details modal
                console.log('View errors for upload:', uploadStatus.uploadId);
              }}
            >
              View Errors ({uploadStatus.details.errorRows})
            </Button>
          )}
        </div>
      )}
    </div>
  );
}
