/**
 * Comprehensive Error Logging System
 * 
 * Provides structured error logging for CSV upload processing
 * with detailed context and debugging information.
 */

export interface ErrorLogEntry {
  id?: string;
  uploadId: string;
  timestamp: string;
  level: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR' | 'CRITICAL';
  category: 'UPLOAD' | 'VALIDATION' | 'PROCESSING' | 'DATABASE' | 'SYSTEM';
  message: string;
  details?: any;
  context?: {
    userId?: string;
    filename?: string;
    rowNumber?: number;
    columnName?: string;
    rawData?: any;
    stackTrace?: string;
  };
}

export interface ErrorSummary {
  totalErrors: number;
  errorsByLevel: { [level: string]: number };
  errorsByCategory: { [category: string]: number };
  mostCommonErrors: { message: string; count: number }[];
  timeRange: { start: string; end: string };
}

/**
 * Error logger class for CSV upload processing
 */
export class ErrorLogger {
  private logs: ErrorLogEntry[] = [];
  private uploadId: string;
  private context: Partial<ErrorLogEntry['context']>;

  constructor(uploadId: string, context: Partial<ErrorLogEntry['context']> = {}) {
    this.uploadId = uploadId;
    this.context = context;
  }

  /**
   * Log a debug message
   */
  debug(message: string, details?: any, additionalContext?: Partial<ErrorLogEntry['context']>) {
    this.log('DEBUG', 'PROCESSING', message, details, additionalContext);
  }

  /**
   * Log an info message
   */
  info(message: string, details?: any, additionalContext?: Partial<ErrorLogEntry['context']>) {
    this.log('INFO', 'PROCESSING', message, details, additionalContext);
  }

  /**
   * Log a warning
   */
  warn(message: string, details?: any, additionalContext?: Partial<ErrorLogEntry['context']>) {
    this.log('WARN', 'VALIDATION', message, details, additionalContext);
  }

  /**
   * Log an error
   */
  error(message: string, details?: any, additionalContext?: Partial<ErrorLogEntry['context']>) {
    this.log('ERROR', 'PROCESSING', message, details, additionalContext);
  }

  /**
   * Log a critical error
   */
  critical(message: string, details?: any, additionalContext?: Partial<ErrorLogEntry['context']>) {
    this.log('CRITICAL', 'SYSTEM', message, details, additionalContext);
  }

  /**
   * Log a validation error
   */
  validationError(message: string, rowNumber?: number, columnName?: string, value?: any, rawData?: any) {
    this.log('ERROR', 'VALIDATION', message, { value }, {
      rowNumber,
      columnName,
      rawData
    });
  }

  /**
   * Log a database error
   */
  databaseError(message: string, error: Error, details?: any) {
    this.log('ERROR', 'DATABASE', message, { 
      error: error.message,
      ...details 
    }, {
      stackTrace: error.stack
    });
  }

  /**
   * Log an upload error
   */
  uploadError(message: string, filename?: string, details?: any) {
    this.log('ERROR', 'UPLOAD', message, details, { filename });
  }

  /**
   * Generic log method
   */
  private log(
    level: ErrorLogEntry['level'],
    category: ErrorLogEntry['category'],
    message: string,
    details?: any,
    additionalContext?: Partial<ErrorLogEntry['context']>
  ) {
    const entry: ErrorLogEntry = {
      uploadId: this.uploadId,
      timestamp: new Date().toISOString(),
      level,
      category,
      message,
      details,
      context: {
        ...this.context,
        ...additionalContext
      }
    };

    this.logs.push(entry);

    // Also log to console for development
    if (process.env.NODE_ENV === 'development') {
      const consoleMethod = level === 'CRITICAL' || level === 'ERROR' ? 'error' :
                           level === 'WARN' ? 'warn' : 'log';
      console[consoleMethod](`[${level}] ${category}: ${message}`, details || '');
    }
  }

  /**
   * Get all logs
   */
  getLogs(): ErrorLogEntry[] {
    return [...this.logs];
  }

  /**
   * Get logs by level
   */
  getLogsByLevel(level: ErrorLogEntry['level']): ErrorLogEntry[] {
    return this.logs.filter(log => log.level === level);
  }

  /**
   * Get logs by category
   */
  getLogsByCategory(category: ErrorLogEntry['category']): ErrorLogEntry[] {
    return this.logs.filter(log => log.category === category);
  }

  /**
   * Get error summary
   */
  getSummary(): ErrorSummary {
    const errorsByLevel: { [level: string]: number } = {};
    const errorsByCategory: { [category: string]: number } = {};
    const messageCounts: { [message: string]: number } = {};

    let earliestTime = new Date().toISOString();
    let latestTime = new Date(0).toISOString();

    this.logs.forEach(log => {
      // Count by level
      errorsByLevel[log.level] = (errorsByLevel[log.level] || 0) + 1;
      
      // Count by category
      errorsByCategory[log.category] = (errorsByCategory[log.category] || 0) + 1;
      
      // Count messages
      messageCounts[log.message] = (messageCounts[log.message] || 0) + 1;
      
      // Track time range
      if (log.timestamp < earliestTime) earliestTime = log.timestamp;
      if (log.timestamp > latestTime) latestTime = log.timestamp;
    });

    // Get most common errors
    const mostCommonErrors = Object.entries(messageCounts)
      .map(([message, count]) => ({ message, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    return {
      totalErrors: this.logs.length,
      errorsByLevel,
      errorsByCategory,
      mostCommonErrors,
      timeRange: {
        start: earliestTime,
        end: latestTime
      }
    };
  }

  /**
   * Export logs as JSON
   */
  exportLogs(): string {
    return JSON.stringify({
      uploadId: this.uploadId,
      context: this.context,
      summary: this.getSummary(),
      logs: this.logs
    }, null, 2);
  }

  /**
   * Clear all logs
   */
  clear() {
    this.logs = [];
  }

  /**
   * Get logs for database insertion
   */
  getLogsForDatabase(): Array<{
    upload_id: string;
    row_number: number | null;
    error_type: string;
    error_message: string;
    field_name: string | null;
    field_value: string | null;
    raw_row_data: any;
  }> {
    return this.logs
      .filter(log => log.level === 'ERROR' || log.level === 'CRITICAL')
      .map(log => ({
        upload_id: this.uploadId,
        row_number: log.context?.rowNumber || null,
        error_type: `${log.level}_${log.category}`,
        error_message: log.message,
        field_name: log.context?.columnName || null,
        field_value: log.details?.value ? String(log.details.value) : null,
        raw_row_data: log.context?.rawData || null
      }));
  }
}

/**
 * Global error logger for system-wide errors
 */
export class SystemErrorLogger {
  private static instance: SystemErrorLogger;
  private logs: ErrorLogEntry[] = [];

  private constructor() {}

  static getInstance(): SystemErrorLogger {
    if (!SystemErrorLogger.instance) {
      SystemErrorLogger.instance = new SystemErrorLogger();
    }
    return SystemErrorLogger.instance;
  }

  logSystemError(message: string, error: Error, context?: any) {
    const entry: ErrorLogEntry = {
      uploadId: 'SYSTEM',
      timestamp: new Date().toISOString(),
      level: 'CRITICAL',
      category: 'SYSTEM',
      message,
      details: {
        error: error.message,
        stack: error.stack,
        ...context
      }
    };

    this.logs.push(entry);
    console.error('[SYSTEM ERROR]', message, error);
  }

  getSystemLogs(): ErrorLogEntry[] {
    return [...this.logs];
  }
}

/**
 * Utility functions for error handling
 */
export const ErrorUtils = {
  /**
   * Format error for user display
   */
  formatUserError(error: ErrorLogEntry): string {
    const prefix = error.context?.rowNumber ? `Row ${error.context.rowNumber}: ` : '';
    const suffix = error.context?.columnName ? ` (${error.context.columnName})` : '';
    return `${prefix}${error.message}${suffix}`;
  },

  /**
   * Group errors by type
   */
  groupErrorsByType(errors: ErrorLogEntry[]): { [type: string]: ErrorLogEntry[] } {
    return errors.reduce((groups, error) => {
      const type = `${error.level}_${error.category}`;
      if (!groups[type]) groups[type] = [];
      groups[type].push(error);
      return groups;
    }, {} as { [type: string]: ErrorLogEntry[] });
  },

  /**
   * Get error statistics
   */
  getErrorStats(errors: ErrorLogEntry[]): {
    total: number;
    byLevel: { [level: string]: number };
    byCategory: { [category: string]: number };
    withRowNumbers: number;
    withoutRowNumbers: number;
  } {
    const byLevel: { [level: string]: number } = {};
    const byCategory: { [category: string]: number } = {};
    let withRowNumbers = 0;
    let withoutRowNumbers = 0;

    errors.forEach(error => {
      byLevel[error.level] = (byLevel[error.level] || 0) + 1;
      byCategory[error.category] = (byCategory[error.category] || 0) + 1;
      
      if (error.context?.rowNumber) {
        withRowNumbers++;
      } else {
        withoutRowNumbers++;
      }
    });

    return {
      total: errors.length,
      byLevel,
      byCategory,
      withRowNumbers,
      withoutRowNumbers
    };
  }
};
