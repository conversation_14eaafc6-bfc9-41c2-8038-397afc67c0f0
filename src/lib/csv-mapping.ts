/**
 * CSV Mapping Configuration for Shopmy Data
 *
 * This file contains the mapping logic to transform Shopmy CSV format
 * to the normalized_transactions database schema.
 */

import { NormalizedTransaction } from '@/types/database';

// Data cleaning configuration - simplified for Excel-like behavior
export const DATA_CLEANING_CONFIG = {
  // Whether to include transactions with negative amounts (returns/refunds)
  // Set to true to include them as negative values in calculations
  INCLUDE_NEGATIVE_TRANSACTIONS: true,

  // Whether to include transactions with zero amounts
  // Set to true to include them (they won't affect GMV but will affect transaction count)
  INCLUDE_ZERO_AMOUNTS: true,

  // Whether to normalize merchant names (remove extra spaces, standardize casing)
  NORMALIZE_MERCHANT_NAMES: true
};

// Shopmy CSV column structure based on the provided sample
export interface ShopmyCsvRow {
  Date: string;                    // "06/24/2025"
  Merchant: string;               // "The Home Depot"
  Domain: string;                 // "homedepot.com"
  'Order Amount USD': string;     // "161.99"
  'Commission USD': string;       // "1.33" or "-" for no commission
  'Code Used': string;            // "SHOPSALE" or "-" for no code
  Status: string;                 // "active"
}

// Alternative column names that might appear in CSV files
export const SHOPMY_COLUMN_ALIASES = {
  date: ['Date', 'Transaction Date', 'Order Date', 'date'],
  merchant: ['Merchant', 'Store', 'Retailer', 'merchant'],
  domain: ['Domain', 'Website', 'URL', 'domain'],
  orderAmount: ['Order Amount USD', 'Order Amount', 'Total', 'Amount', 'order_amount'],
  commission: ['Commission USD', 'Commission', 'Earnings', 'commission'],
  codeUsed: ['Code Used', 'Coupon Code', 'Promo Code', 'code_used', 'code'],
  status: ['Status', 'Transaction Status', 'status']
};

// Required columns for successful processing
export const REQUIRED_COLUMNS = ['date', 'merchant', 'orderAmount', 'status'];

// Status mapping from Shopmy to normalized format
export const STATUS_MAPPING: { [key: string]: string } = {
  'active': 'confirmed',
  'pending': 'pending',
  'declined': 'declined',
  'cancelled': 'cancelled',
  'confirmed': 'confirmed',
  'failed': 'declined'
};

/**
 * Normalize column names to match our internal format
 */
export function normalizeColumnName(columnName: string): string | null {
  const normalized = columnName.trim().toLowerCase();

  for (const [key, aliases] of Object.entries(SHOPMY_COLUMN_ALIASES)) {
    if (aliases.some(alias => alias.toLowerCase() === normalized)) {
      return key;
    }
  }

  return null;
}

/**
 * Validate CSV headers and return normalized column mapping
 */
export function validateAndMapHeaders(headers: string[]): {
  isValid: boolean;
  mapping: { [key: string]: number };
  errors: string[];
} {
  const mapping: { [key: string]: number } = {};
  const errors: string[] = [];
  const foundColumns = new Set<string>();

  // Map each header to our normalized format
  headers.forEach((header, index) => {
    const normalizedName = normalizeColumnName(header);
    if (normalizedName) {
      mapping[normalizedName] = index;
      foundColumns.add(normalizedName);
    }
  });

  // Check for required columns
  REQUIRED_COLUMNS.forEach(required => {
    if (!foundColumns.has(required)) {
      errors.push(`Missing required column: ${required}`);
    }
  });

  return {
    isValid: errors.length === 0,
    mapping,
    errors
  };
}

/**
 * Parse date from various formats
 */
export function parseDate(dateString: string): Date | null {
  if (!dateString || dateString.trim() === '' || dateString === '-') {
    return null;
  }

  // Try different date formats

  const cleanDate = dateString.trim();

  // Try MM/DD/YYYY format first (Shopmy default)
  const mmddyyyy = cleanDate.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/);
  if (mmddyyyy) {
    const [, month, day, year] = mmddyyyy;
    const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
    if (!isNaN(date.getTime())) {
      return date;
    }
  }

  // Try standard Date parsing as fallback
  const date = new Date(cleanDate);
  return isNaN(date.getTime()) ? null : date;
}

/**
 * Parse monetary amount from string with enhanced cleaning
 */
export function parseAmount(amountString: string, preserveSign: boolean = false): number {
  if (!amountString || amountString.trim() === '' || amountString === '-') {
    return 0;
  }

  // Remove currency symbols, commas, and whitespace
  const cleanAmount = amountString
    .replace(/[$,\s]/g, '')
    .trim();

  const amount = parseFloat(cleanAmount);
  if (isNaN(amount)) {
    return 0;
  }

  // Return signed amount if preserveSign is true, otherwise absolute value
  return preserveSign ? amount : Math.abs(amount);
}

/**
 * Normalize merchant name for consistency
 */
export function normalizeMerchantName(merchantName: string): string {
  if (!merchantName) return '';

  return merchantName
    .trim()
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .replace(/[^\w\s&.-]/g, '') // Remove special characters except common business ones
    .toLowerCase()
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1)) // Title case
    .join(' ');
}

/**
 * Check if transaction should be filtered out - NO FILTERING, just sum everything
 */
export function shouldFilterTransaction(
  orderAmount: number,
  commissionAmount: number,
  merchantName: string
): { shouldFilter: boolean; reason?: string } {
  // NO FILTERING AT ALL - include everything for pure Excel-like GMV calculation
  // Include empty merchant names, zero amounts, negative amounts, everything
  return { shouldFilter: false };
}

/**
 * Generate a unique transaction ID
 */
export function generateTransactionId(row: any, rowIndex: number, uploadId: string): string {
  // Create a hash-like ID based on key fields
  const date = row.date || '';
  const merchant = row.merchant || '';
  const amount = row.orderAmount || '';

  // Simple hash function for generating consistent IDs
  const hashInput = `${uploadId}-${date}-${merchant}-${amount}-${rowIndex}`;
  return `shopmy-${hashInput.replace(/[^a-zA-Z0-9]/g, '').toLowerCase()}`;
}

/**
 * Generate a duplicate detection key for a transaction
 */
export function generateDuplicateKey(
  date: Date,
  merchantName: string,
  orderAmount: number,
  commissionAmount: number
): string {
  const dateStr = date.toISOString().split('T')[0]; // YYYY-MM-DD format
  const normalizedMerchant = normalizeMerchantName(merchantName);

  // Create a key that uniquely identifies potentially duplicate transactions
  return `${dateStr}-${normalizedMerchant}-${orderAmount.toFixed(2)}-${commissionAmount.toFixed(2)}`;
}

/**
 * Check if transaction is a potential duplicate
 */
export function isDuplicateTransaction(
  transactionKey: string,
  existingKeys: Set<string>
): boolean {
  return existingKeys.has(transactionKey);
}

/**
 * Transform a CSV row to normalized transaction format with simplified cleaning
 */
export function transformCsvRowToTransaction(
  csvRow: any,
  columnMapping: { [key: string]: number }
): {
  transaction?: Partial<NormalizedTransaction>;
  filtered?: boolean;
  filterReason?: string;
} {
  // Extract values using column mapping
  const getValue = (key: string): string => {
    const columnIndex = columnMapping[key];
    return columnIndex !== undefined ? (csvRow[columnIndex] || '').toString().trim() : '';
  };

  const dateValue = getValue('date');
  const merchantValue = getValue('merchant');
  const domainValue = getValue('domain');
  const orderAmountValue = getValue('orderAmount');
  const commissionValue = getValue('commission');
  const codeUsedValue = getValue('codeUsed');
  const statusValue = getValue('status');

  // Parse and validate data
  const transactionDate = parseDate(dateValue);
  if (!transactionDate) {
    return { filtered: true, filterReason: `Invalid date format: ${dateValue}` };
  }

  // Parse amounts with sign preservation for filtering logic
  const orderAmount = parseAmount(orderAmountValue, true); // Preserve sign for filtering
  const commissionAmount = parseAmount(commissionValue, true); // Preserve sign for filtering

  // Normalize merchant name if enabled, handle empty names gracefully
  const normalizedMerchantName = DATA_CLEANING_CONFIG.NORMALIZE_MERCHANT_NAMES
    ? normalizeMerchantName(merchantValue || 'Unknown Merchant')
    : (merchantValue || 'Unknown Merchant');

  // Check if transaction should be filtered (NO FILTERING - always include)
  const filterCheck = shouldFilterTransaction(orderAmount, commissionAmount, normalizedMerchantName);
  if (filterCheck.shouldFilter) {
    return { filtered: true, filterReason: filterCheck.reason };
  }

  const normalizedStatus = STATUS_MAPPING[statusValue.toLowerCase()] || 'pending';

  // Generate transaction ID
  const transactionId = `shopmy-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

  // Determine transaction type based on amount sign
  const isReturn = orderAmount < 0;
  const transactionType = isReturn ? 'return' : 'sale';
  const finalStatus = isReturn ? 'confirmed' : normalizedStatus; // Returns are typically confirmed

  // Create normalized transaction with absolute values for storage
  const normalizedTransaction: Partial<NormalizedTransaction> = {
    transaction_id: transactionId,
    platform: 'shopmy',
    source_transaction_id: transactionId,
    currency: 'USD',
    order_amount: Math.abs(orderAmount),
    commission_amount: Math.abs(commissionAmount),
    final_order_amount: Math.abs(orderAmount),
    final_commission_amount: Math.abs(commissionAmount),
    order_id: transactionId,
    customer_id: undefined,
    transaction_date: transactionDate.toISOString(),
    created_date: new Date().toISOString(),
    network_name: 'ShopMy',
    merchant_name: normalizedMerchantName,
    merchant_id: domainValue || undefined,
    connection_name: domainValue || undefined,
    status: finalStatus,
    transaction_type: transactionType,
    decline_reason: finalStatus === 'declined' ? 'Unknown' : undefined,
    channel_name: 'affiliate',
    custom_fields: {
      domain: domainValue,
      code_used: codeUsedValue !== '-' ? codeUsedValue : undefined,
      original_status: statusValue,
      is_return: isReturn,
      original_order_amount: orderAmount, // Store original signed amount
      original_commission_amount: commissionAmount
    },
    comments: [
      codeUsedValue !== '-' ? `Promo code used: ${codeUsedValue}` : undefined,
      isReturn ? 'Return/Refund transaction' : undefined
    ].filter(Boolean).join('; ') || undefined,
    last_updated: new Date().toISOString()
  };

  return { transaction: normalizedTransaction };
}

/**
 * Validate a transformed transaction before insertion
 */
export function validateTransaction(transaction: Partial<NormalizedTransaction>): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  // Check required fields
  if (!transaction.transaction_id) errors.push('Transaction ID is required');
  if (!transaction.platform) errors.push('Platform is required');
  if (!transaction.merchant_name) errors.push('Merchant name is required');
  if (!transaction.transaction_date) errors.push('Transaction date is required');
  if (!transaction.order_amount || transaction.order_amount <= 0) {
    errors.push('Valid order amount is required');
  }
  if (!transaction.status) errors.push('Status is required');

  return {
    isValid: errors.length === 0,
    errors
  };
}
