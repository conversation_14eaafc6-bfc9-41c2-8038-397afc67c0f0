import { format, parseISO } from "date-fns";
import { formatInTimeZone, toZonedTime } from "date-fns-tz";

/**
 * Get the user's timezone
 */
export function getUserTimezone(): string {
  return Intl.DateTimeFormat().resolvedOptions().timeZone;
}

/**
 * Convert a Date object to YYYY-MM-DD string in local timezone
 * This prevents timezone shift issues when converting dates
 */
export function dateToLocalDateString(date: Date): string {
  const timezone = getUserTimezone();
  return formatInTimeZone(date, timezone, 'yyyy-MM-dd');
}

/**
 * Convert a YYYY-MM-DD string to Date object in local timezone
 * This ensures the date represents the correct day in user's timezone
 * Uses noon time to avoid DST edge cases
 */
export function localDateStringToDate(dateString: string): Date {
  // Create date at noon to avoid timezone/DST issues (team is EST-based)
  return new Date(dateString + 'T12:00:00');
}

/**
 * Safe conversion from YYYY-MM-DD string to Date for date pickers
 * This prevents timezone shift issues when displaying dates in pickers
 */
export function safeStringToDate(dateString: string): Date {
  // Parse the date string as local date (not UTC)
  const [year, month, day] = dateString.split('-').map(Number);
  // Create date at noon to avoid any DST edge cases
  return new Date(year, month - 1, day, 12, 0, 0, 0);
}

/**
 * Get start of day in user's timezone for a given date
 * Returns 00:00:00 for the given date
 */
export function getStartOfDay(date: Date): Date {
  // Extract date part and create start of day (team is EST-based)
  const dateString = date.toISOString().split('T')[0];
  return new Date(dateString + 'T00:00:00');
}

/**
 * Get end of day in user's timezone for a given date
 * Returns 23:59:59 for the given date
 */
export function getEndOfDay(date: Date): Date {
  // Extract date part and create end of day (team is EST-based)
  const dateString = date.toISOString().split('T')[0];
  return new Date(dateString + 'T23:59:59');
}

/**
 * Format date for display (handles timezone properly)
 */
export function formatDateForDisplay(date: Date | string): string {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  const timezone = getUserTimezone();
  return formatInTimeZone(dateObj, timezone, 'PPP');
}

/**
 * Check if a transaction date falls within a date range (inclusive)
 * Compares dates in YYYY-MM-DD format to avoid timezone issues
 */
export function isDateInRange(
  transactionDate: string | Date,
  startDate?: string,
  endDate?: string
): boolean {
  // Convert transaction date to YYYY-MM-DD format
  const transactionDateObj = typeof transactionDate === 'string'
    ? parseISO(transactionDate)
    : transactionDate;

  const transactionDateString = dateToLocalDateString(transactionDateObj);

  // Compare date strings directly (inclusive range)
  if (startDate && transactionDateString < startDate) {
    return false;
  }

  if (endDate && transactionDateString > endDate) {
    return false;
  }

  return true;
}

/**
 * Get date range for predefined time periods (7d, 30d, 90d)
 * Returns dates in YYYY-MM-DD format
 */
export function getTimeRangeDates(timeRange: string): { startDate: string; endDate: string } {
  const now = new Date();

  // Simple date formatting without timezone conversion
  const endDate = now.toISOString().substring(0, 10); // Gets "2025-06-24"

  let daysToSubtract: number;
  switch (timeRange) {
    case '7d':
      daysToSubtract = 7;
      break;
    case '30d':
      daysToSubtract = 30;
      break;
    case '90d':
      daysToSubtract = 90;
      break;
    default:
      daysToSubtract = 30;
  }

  const startDateObj = new Date(now);
  startDateObj.setDate(now.getDate() - daysToSubtract);
  const startDate = startDateObj.toISOString().substring(0, 10); // Gets "2025-05-25"

  return { startDate, endDate };
}
