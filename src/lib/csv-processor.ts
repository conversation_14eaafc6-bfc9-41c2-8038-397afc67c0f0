/**
 * CSV Processing Engine
 *
 * Handles the complete CSV processing pipeline including parsing,
 * validation, transformation, and database insertion.
 */

import <PERSON> from 'papaparse';
import { NormalizedTransaction } from '@/types/database';
import {
  validateAndMapHeaders,
  transformCsvRowToTransaction,
  validateTransaction
} from './csv-mapping';

export interface ProcessingResult {
  success: boolean;
  totalRows: number;
  processedRows: number;
  successfulRows: number;
  errorRows: number;
  duplicateRows: number;
  errors: ProcessingError[];
  uploadId: string;
}

export interface ProcessingError {
  rowNumber: number;
  errorType: string;
  errorMessage: string;
  fieldName?: string;
  fieldValue?: string;
  rawRowData?: any;
}

export interface ProcessingOptions {
  batchSize?: number;
  skipDuplicates?: boolean;
  validateOnly?: boolean;
  onProgress?: (progress: ProcessingProgress) => void;
}

export interface ProcessingProgress {
  totalRows: number;
  processedRows: number;
  successfulRows: number;
  errorRows: number;
  duplicateRows: number;
  currentBatch: number;
  totalBatches: number;
  percentage: number;
}

/**
 * Main CSV processing class
 */
export class CsvProcessor {
  private supabase;
  private uploadId: string;
  private filename: string;
  private userId: string;
  private options: ProcessingOptions;

  constructor(
    uploadId: string,
    filename: string,
    userId: string,
    supabaseClient: any,
    options: ProcessingOptions = {}
  ) {
    this.supabase = supabaseClient;
    this.uploadId = uploadId;
    this.filename = filename;
    this.userId = userId;
    this.options = {
      batchSize: 100,
      skipDuplicates: true,
      validateOnly: false,
      ...options
    };
  }

  /**
   * Process CSV file from file content
   */
  async processFile(fileContent: string): Promise<ProcessingResult> {
    const result: ProcessingResult = {
      success: false,
      totalRows: 0,
      processedRows: 0,
      successfulRows: 0,
      errorRows: 0,
      duplicateRows: 0,
      errors: [],
      uploadId: this.uploadId
    };

    try {
      // Update upload status to processing
      await this.updateUploadStatus('processing', {
        processing_started_at: new Date().toISOString()
      });

      // Parse CSV
      const parseResult = Papa.parse(fileContent, {
        header: false,
        skipEmptyLines: true,
        transformHeader: (header: string) => header.trim()
      });

      if (parseResult.errors.length > 0) {
        throw new Error(`CSV parsing failed: ${parseResult.errors[0].message}`);
      }

      const rows = parseResult.data as string[][];
      if (rows.length < 2) {
        throw new Error('CSV file must contain at least a header row and one data row');
      }

      // Extract headers and validate
      const headers = rows[0];
      const headerValidation = validateAndMapHeaders(headers);

      if (!headerValidation.isValid) {
        throw new Error(`Invalid CSV format: ${headerValidation.errors.join(', ')}`);
      }

      const dataRows = rows.slice(1);
      result.totalRows = dataRows.length;

      // Process rows in batches
      const batchSize = this.options.batchSize || 100;
      const totalBatches = Math.ceil(dataRows.length / batchSize);

      for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
        const startIndex = batchIndex * batchSize;
        const endIndex = Math.min(startIndex + batchSize, dataRows.length);
        const batchRows = dataRows.slice(startIndex, endIndex);

        const batchResult = await this.processBatch(
          batchRows,
          headerValidation.mapping,
          startIndex,
          batchIndex + 1,
          totalBatches
        );

        result.processedRows += batchResult.processedRows;
        result.successfulRows += batchResult.successfulRows;
        result.errorRows += batchResult.errorRows;
        result.duplicateRows += batchResult.duplicateRows;
        result.errors.push(...batchResult.errors);

        // Report progress
        if (this.options.onProgress) {
          this.options.onProgress({
            totalRows: result.totalRows,
            processedRows: result.processedRows,
            successfulRows: result.successfulRows,
            errorRows: result.errorRows,
            duplicateRows: result.duplicateRows,
            currentBatch: batchIndex + 1,
            totalBatches,
            percentage: Math.round((result.processedRows / result.totalRows) * 100)
          });
        }
      }

      result.success = result.errorRows === 0 || result.successfulRows > 0;

      // Update upload status
      await this.updateUploadStatus(
        result.success ? 'completed' : 'failed',
        {
          processing_completed_at: new Date().toISOString(),
          total_rows: result.totalRows,
          processed_rows: result.processedRows,
          successful_rows: result.successfulRows,
          error_rows: result.errorRows,
          duplicate_rows: result.duplicateRows,
          error_message: result.success ? null : 'Processing completed with errors'
        }
      );

      // Log errors to database
      if (result.errors.length > 0) {
        await this.logErrors(result.errors);
      }

      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

      await this.updateUploadStatus('failed', {
        processing_completed_at: new Date().toISOString(),
        error_message: errorMessage
      });

      result.errors.push({
        rowNumber: 0,
        errorType: 'PROCESSING_ERROR',
        errorMessage
      });

      return result;
    }
  }

  /**
   * Process a batch of CSV rows
   */
  private async processBatch(
    rows: string[][],
    columnMapping: { [key: string]: number },
    startRowIndex: number,
    batchNumber: number,
    totalBatches: number
  ): Promise<{
    processedRows: number;
    successfulRows: number;
    errorRows: number;
    duplicateRows: number;
    errors: ProcessingError[];
  }> {
    const batchResult = {
      processedRows: 0,
      successfulRows: 0,
      errorRows: 0,
      duplicateRows: 0,
      errors: [] as ProcessingError[]
    };

    const transactionsToInsert: Partial<NormalizedTransaction>[] = [];

    // Transform rows to transactions
    for (let i = 0; i < rows.length; i++) {
      const rowIndex = startRowIndex + i;
      const row = rows[i];
      batchResult.processedRows++;

      try {
        // Transform CSV row to transaction
        const transaction = transformCsvRowToTransaction(
          row,
          columnMapping
        );

        // Validate transaction
        const validation = validateTransaction(transaction);
        if (!validation.isValid) {
          throw new Error(validation.errors.join(', '));
        }

        // Check for duplicates if enabled
        if (this.options.skipDuplicates) {
          const isDuplicate = await this.checkForDuplicate(transaction);
          if (isDuplicate) {
            batchResult.duplicateRows++;
            continue;
          }
        }

        transactionsToInsert.push(transaction);

      } catch (error) {
        batchResult.errorRows++;
        batchResult.errors.push({
          rowNumber: rowIndex + 2, // +2 because CSV is 1-indexed and we skip header
          errorType: 'TRANSFORMATION_ERROR',
          errorMessage: error instanceof Error ? error.message : 'Unknown transformation error',
          rawRowData: row
        });
      }
    }

    // Insert transactions if not in validation-only mode
    if (!this.options.validateOnly && transactionsToInsert.length > 0) {
      try {
        const { error } = await this.supabase
          .from('normalized_transactions')
          .insert(transactionsToInsert);

        if (error) {
          throw error;
        }

        batchResult.successfulRows = transactionsToInsert.length;
      } catch (error) {
        // If batch insert fails, try individual inserts to identify specific failures
        for (let j = 0; j < transactionsToInsert.length; j++) {
          const transaction = transactionsToInsert[j];
          try {
            const { error } = await this.supabase
              .from('normalized_transactions')
              .insert([transaction]);

            if (error) {
              throw error;
            }

            batchResult.successfulRows++;
          } catch (insertError) {
            batchResult.errorRows++;
            batchResult.errors.push({
              rowNumber: startRowIndex + j + 2, // +2 because CSV is 1-indexed and we skip header
              errorType: 'INSERT_ERROR',
              errorMessage: insertError instanceof Error ? insertError.message : 'Database insert failed'
            });
          }
        }
      }
    } else if (this.options.validateOnly) {
      batchResult.successfulRows = transactionsToInsert.length;
    }

    return batchResult;
  }

  /**
   * Check if a transaction already exists (duplicate detection)
   */
  private async checkForDuplicate(transaction: Partial<NormalizedTransaction>): Promise<boolean> {
    try {
      const { data, error } = await this.supabase
        .from('normalized_transactions')
        .select('id')
        .eq('platform', 'shopmy')
        .eq('merchant_name', transaction.merchant_name)
        .eq('order_amount', transaction.order_amount)
        .eq('transaction_date', transaction.transaction_date)
        .limit(1);

      if (error) {
        console.warn('Error checking for duplicates:', error);
        return false; // Don't skip on error
      }

      return data && data.length > 0;
    } catch (error) {
      console.warn('Error checking for duplicates:', error);
      return false;
    }
  }

  /**
   * Update upload status in database
   */
  private async updateUploadStatus(status: string, updates: any = {}) {
    try {
      await this.supabase
        .from('csv_uploads')
        .update({
          status,
          updated_at: new Date().toISOString(),
          ...updates
        })
        .eq('id', this.uploadId);
    } catch (error) {
      console.error('Error updating upload status:', error);
    }
  }

  /**
   * Log errors to database
   */
  private async logErrors(errors: ProcessingError[]) {
    try {
      const errorRecords = errors.map(error => ({
        upload_id: this.uploadId,
        row_number: error.rowNumber,
        error_type: error.errorType,
        error_message: error.errorMessage,
        field_name: error.fieldName || null,
        field_value: error.fieldValue || null,
        raw_row_data: error.rawRowData || null
      }));

      await this.supabase
        .from('csv_upload_errors')
        .insert(errorRecords);
    } catch (error) {
      console.error('Error logging errors to database:', error);
    }
  }
}

/**
 * Utility function to validate CSV file before processing
 */
export async function validateCsvFile(fileContent: string): Promise<{
  isValid: boolean;
  errors: string[];
  rowCount: number;
  headers: string[];
}> {
  try {
    // First, get the actual row count by parsing the entire file
    const fullParseResult = Papa.parse(fileContent, {
      header: false,
      skipEmptyLines: true
    });

    if (fullParseResult.errors.length > 0) {
      return {
        isValid: false,
        errors: [`CSV parsing failed: ${fullParseResult.errors[0].message}`],
        rowCount: 0,
        headers: []
      };
    }

    const allRows = fullParseResult.data as string[][];
    if (allRows.length < 2) {
      return {
        isValid: false,
        errors: ['CSV file must contain at least a header row and one data row'],
        rowCount: 0,
        headers: []
      };
    }

    const headers = allRows[0];
    const headerValidation = validateAndMapHeaders(headers);

    return {
      isValid: headerValidation.isValid,
      errors: headerValidation.errors,
      rowCount: allRows.length - 1, // Exclude header
      headers
    };

  } catch (error) {
    return {
      isValid: false,
      errors: [error instanceof Error ? error.message : 'Unknown validation error'],
      rowCount: 0,
      headers: []
    };
  }
}
